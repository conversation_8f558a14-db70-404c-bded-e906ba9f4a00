{"name": "pump-fun-api-tester", "version": "1.0.0", "description": "CORS proxy server and web interface for testing pump.fun API endpoints", "main": "cors-proxy-server.js", "scripts": {"start": "node cors-proxy-server.js", "dev": "nodemon cors-proxy-server.js", "market-cache": "node market-cache-server.js", "market-dev": "nodemon market-cache-server.js", "start-all": "node start-servers.js", "test": "echo \"No tests specified\" && exit 0"}, "keywords": ["pump.fun", "api", "cors", "proxy", "solana", "crypto"], "author": "Chad GPT Development Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "node-fetch": "^2.7.0"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=14.0.0"}}