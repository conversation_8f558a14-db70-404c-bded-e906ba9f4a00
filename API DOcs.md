# Pump.fun API Documentation

## Overview
This document outlines all pump.fun API endpoints, usage patterns, and integration methods used in the Chad GPT system.

## Table of Contents
- [Core API Endpoints](#core-api-endpoints)
- [Advanced API Endpoints](#advanced-api-endpoints)
- [Moralis Integration](#moralis-integration)

- [Data Aggregation Strategy](#data-aggregation-strategy)
- [Rate Limiting & Best Practices](#rate-limiting--best-practices)

## Core API Endpoints

### 1. Frontend API v3 (Primary)
**Base URL:** `https://frontend-api-v3.pump.fun`

#### Get Coins List
```http
GET /coins?sort={sort_field}&order={order}&limit={limit}&includeNsfw={boolean}
```

**Parameters:**
- `sort`: `created_timestamp`, `last_trade_timestamp`, `market_cap`, `volume_24h`
- `order`: `ASC`, `DESC`
- `limit`: Number of tokens (1-100)
- `includeNsfw`: `true`, `false`

**Example:**
```bash
curl "https://frontend-api-v3.pump.fun/coins?sort=created_timestamp&order=DESC&limit=50&includeNsfw=false"
```

#### Get Graduated Tokens
```http
GET /coins/graduated?limit={limit}&offset={offset}
```

**Example:**
```bash
curl "https://frontend-api-v3.pump.fun/coins/graduated?limit=50&offset=0"
```

#### Get Individual Token
```http
GET /coins/{mint_address}
```

**Example:**
```bash
curl "https://frontend-api-v3.pump.fun/coins/9BB6NFEcjBCtnNLFko2FqVQBq8HHM13kCyYcdQbgpump"
```

### 2. Advanced API v2 (Enhanced Data)
**Base URL:** `https://advanced-api-v2.pump.fun`

#### Get Enhanced Coins List
```http
GET /coins/list?sortBy={field}&limit={limit}&offset={offset}
```

**Parameters:**
- `sortBy`: `marketCap`, `volume`, `createdAt`, `lastTradeAt`
- `limit`: Number of tokens (1-100)
- `offset`: Pagination offset

**Example:**
```bash
curl "https://advanced-api-v2.pump.fun/coins/list?sortBy=marketCap&limit=50&offset=0"
```

#### Get Graduated Tokens (Enhanced)
```http
GET /coins/graduated?limit={limit}&offset={offset}
```

### 3. Legacy API
**Base URL:** `https://pump.fun/api`

#### Get Flags/Categories
```http
GET /flags
```

#### Get Token Details
```http
GET /coins/{mint_address}
```

## Advanced API Endpoints

### Trading Data
```http
GET /coins/{mint_address}/trades
GET /coins/{mint_address}/holders
GET /coins/{mint_address}/volume
```

### Market Data
```http
GET /market/trending
GET /market/new
GET /market/volume-leaders
```

## Moralis Integration

### Bonding Status API
**Base URL:** `https://solana-gateway.moralis.io`

#### Get Token Bonding Status
```http
GET /token/mainnet/{mint_address}/bonding-status
```

**Headers:**
```http
X-API-Key: YOUR_MORALIS_API_KEY
Accept: application/json
```

**Response:**
```json
{
  "mint": "9BB6NFEcjBCtnNLFko2FqVQBq8HHM13kCyYcdQbgpump",
  "bondingProgress": 96.788763272986
}
```

**Example:**
```javascript
const options = {
  method: 'GET',
  headers: {
    accept: 'application/json',
    'X-API-Key': 'YOUR_API_KEY'
  },
};

fetch('https://solana-gateway.moralis.io/token/mainnet/9BB6NFEcjBCtnNLFko2FqVQBq8HHM13kCyYcdQbgpump/bonding-status', options)
  .then(response => response.json())
  .then(response => console.log(response))
  .catch(err => console.error(err));
```

## Token Categories & Sources

### How Pump.fun Categorizes Tokens

#### `trending` - High Volume/Activity Tokens
Pump.fun determines trending tokens based on:
- Recent trading volume
- Market cap growth
- Trading activity frequency
- Time-weighted popularity

**API Endpoints:**
- `https://advanced-api-v2.pump.fun/coins/list?sortBy=volume&limit=100`
- `https://advanced-api-v2.pump.fun/coins/list?sortBy=marketCap&limit=100`
- `https://frontend-api-v3.pump.fun/coins?sort=last_trade_timestamp&order=DESC&limit=50`

#### `graduated` - Tokens That Completed Bonding Curve
Tokens that have reached 100% bonding curve progress and moved to Raydium:
- Completed bonding curve (reached ~$69K market cap)
- Migrated to Raydium DEX
- No longer on pump.fun bonding curve

**API Endpoints:**
- `https://advanced-api-v2.pump.fun/coins/graduated?sortBy=creationTime&limit=100`
- `https://advanced-api-v2.pump.fun/coins/graduated?sortBy=marketCap&limit=100`

#### `forYou` - Pump.fun's Recommended/Curated Tokens
Pump.fun's algorithmic recommendations based on:
- User behavior patterns
- Token quality metrics
- Community engagement
- Platform curation

**API Endpoints:**
- `https://frontend-api-v3.pump.fun/coins/for-you?offset=0&limit=48&includeNsfw=false` (Primary)

**Example:**
```bash
curl "https://frontend-api-v3.pump.fun/coins/for-you?offset=0&limit=48&includeNsfw=false"
```

## Data Aggregation Strategy

### Multi-Source Data Collection
For comprehensive token data, combine multiple pump.fun APIs:

1. **Frontend API v3** - Primary token data
2. **Advanced API v2** - Enhanced metrics
3. **Legacy API** - Fallback data
4. **Moralis API** - Bonding curve progress

### Data Priority System
1. **Moralis bonding progress** (most accurate)
2. **Virtual token reserves** (pump.fun calculation)
3. **Market cap estimation** (fallback only)

### Field Mapping
```javascript
{
  // Core fields
  mint: string,
  name: string,
  symbol: string,
  description: string,
  image_uri: string,
  creator: string,
  created_timestamp: number,
  
  // Market data
  usd_market_cap: number,
  volume: number,
  num_holders: number,
  
  // Bonding curve
  complete: boolean,
  virtual_sol_reserves: number,
  virtual_token_reserves: number,
  bonding_progress: number, // From Moralis
  
  // Metadata
  nsfw: boolean,
  is_banned: boolean,
  category: string,
  last_updated: number
}
```

## Rate Limiting & Best Practices

### Rate Limits
- **Frontend API v3**: ~60 requests/minute
- **Advanced API v2**: ~30 requests/minute  
- **Moralis API**: 25,000 requests/month (free tier)

### Best Practices

#### 1. Request Headers
```javascript
{
  'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
  'Accept': 'application/json',
  'Origin': 'https://pump.fun',
  'Referer': 'https://pump.fun/',
  'Cache-Control': 'no-cache'
}
```

#### 2. Error Handling
```javascript
try {
  const response = await fetch(endpoint, {
    headers: getHeaders(),
    timeout: 10000
  });
  
  if (!response.ok) {
    throw new Error(`API error: ${response.status}`);
  }
  
  return await response.json();
} catch (error) {
  console.error('API request failed:', error);
  return null;
}
```

#### 3. Retry Logic
```javascript
const RETRY_CONFIG = {
  maxRetries: 8,
  baseDelay: 1500,
  maxDelay: 45000,
  backoffMultiplier: 1.8
};

async function fetchWithRetry(url, maxRetries = RETRY_CONFIG.maxRetries) {
  for (let attempt = 0; attempt < maxRetries; attempt++) {
    try {
      const response = await fetch(url);
      if (response.ok) return await response.json();
    } catch (error) {
      if (attempt === maxRetries - 1) throw error;
      
      const delay = Math.min(
        RETRY_CONFIG.baseDelay * Math.pow(RETRY_CONFIG.backoffMultiplier, attempt),
        RETRY_CONFIG.maxDelay
      );
      
      await sleep(delay);
    }
  }
}
```

#### 4. Data Validation
```javascript
function validateTokenData(token) {
  const required = ['mint', 'symbol', 'name'];
  const missing = required.filter(field => !token[field]);
  
  if (missing.length > 0) {
    console.warn(`Missing required fields: ${missing.join(', ')}`);
    return false;
  }
  
  return true;
}
```

## Environment Variables

### Required Configuration
```bash
# Moralis API (for bonding status)
MORALIS_API_KEY=your_moralis_api_key_here

# Optional: Other integrations
OPENROUTER_API_KEY=your_openrouter_key_here
MODEL_NAME=x-ai/grok-4
```

## Usage Examples

### Frontend Integration
```typescript
// Fetch tokens directly from pump.fun APIs
const fetchTokens = async (category: string) => {
  const endpoints = {
    trending: 'https://advanced-api-v2.pump.fun/coins/list?sortBy=volume&limit=100',
    graduated: 'https://advanced-api-v2.pump.fun/coins/graduated?limit=100',
    forYou: 'https://frontend-api-v3.pump.fun/coins/for-you?offset=0&limit=48&includeNsfw=false'
  };

  try {
    const response = await fetch(endpoints[category]);
    const data = await response.json();
    return Array.isArray(data) ? data : data.coins || [];
  } catch (error) {
    console.error('API request failed:', error);
    return [];
  }
};
```

### Bonding Curve Calculation
```typescript
function calculateBondingProgress(token: Token): number {
  // Priority 1: Use Moralis data
  if (token.bonding_progress) {
    return Math.max(0, Math.min(token.bonding_progress, 100));
  }
  
  // Priority 2: Virtual reserves calculation
  if (token.virtual_token_reserves) {
    const INITIAL_RESERVES = 1_073_000_000 * 1_000_000;
    const TOKENS_TO_COLLECT = 793_100_000 * 1_000_000;
    const collected = INITIAL_RESERVES - token.virtual_token_reserves;
    return (collected * 100) / TOKENS_TO_COLLECT;
  }
  
  // Priority 3: Market cap estimation
  if (token.usd_market_cap) {
    return (token.usd_market_cap / 69000) * 100;
  }
  
  return 0;
}
```

## API Response Examples

### Token Data Structure
```json
{
  "mint": "9BB6NFEcjBCtnNLFko2FqVQBq8HHM13kCyYcdQbgpump",
  "name": "Example Token",
  "symbol": "EXAMPLE",
  "description": "An example pump.fun token",
  "image_uri": "https://ipfs.io/ipfs/...",
  "creator": "5gNMozcPTVPb8MLPkMJSq5yWXtmQo9xAgkFUqpUahujK",
  "created_timestamp": 1754069156018,
  "complete": false,
  "virtual_sol_reserves": 41975561178,
  "virtual_token_reserves": 766874799636370,
  "total_supply": 1000000000000000,
  "usd_market_cap": 9111.33,
  "volume": 58.4285,
  "num_holders": 33,
  "bonding_progress": 28.63,
  "nsfw": false,
  "is_banned": false,
  "category": "trending",
  "last_updated": 1754080202026
}
```

## WebSocket Integration

### Real-time Trading Data
**WebSocket URL:** `wss://frontend-api-v3.pump.fun`

#### Subscribe to Trade Events
```javascript
const ws = new WebSocket('wss://frontend-api-v3.pump.fun');

ws.onopen = () => {
  // Subscribe to trade events
  ws.send(JSON.stringify({
    type: 'subscribe',
    channel: 'tradeCreated'
  }));
};

ws.onmessage = (event) => {
  const data = JSON.parse(event.data);
  if (data.type === 'tradeCreated') {
    console.log('New trade:', data.trade);
  }
};
```

#### Trade Event Structure
```json
{
  "type": "tradeCreated",
  "trade": {
    "mint": "9BB6NFEcjBCtnNLFko2FqVQBq8HHM13kCyYcdQbgpump",
    "sol_amount": 1000000,
    "token_amount": 50000000000,
    "is_buy": true,
    "user": "5gNMozcPTVPb8MLPkMJSq5yWXtmQo9xAgkFUqpUahujK",
    "timestamp": 1754080202026,
    "signature": "transaction_signature_here"
  }
}
```

## Data Structure

### Token Data Schema
Standard token object structure from pump.fun APIs:

```typescript
interface PumpFunToken {
  // Core identifiers
  mint: string;
  name: string;
  symbol: string;
  description: string;
  image_uri: string;
  creator: string;
  created_timestamp: number;

  // Market data
  usd_market_cap: number;
  volume: number;
  num_holders: number;

  // Bonding curve
  complete: boolean;
  virtual_sol_reserves: number;
  virtual_token_reserves: number;
  bonding_progress?: number; // From Moralis

  // Metadata
  nsfw: boolean;
  is_banned: boolean;
  total_supply: number;
}
```

## Error Codes & Troubleshooting

### Common HTTP Status Codes
- **200 OK** - Request successful
- **400 Bad Request** - Invalid parameters
- **404 Not Found** - Token/endpoint not found
- **429 Too Many Requests** - Rate limit exceeded
- **500 Internal Server Error** - Server error
- **503 Service Unavailable** - API temporarily down

### Error Response Format
```json
{
  "error": {
    "code": "RATE_LIMIT_EXCEEDED",
    "message": "Too many requests. Please try again later.",
    "details": {
      "limit": 60,
      "window": "1 minute",
      "retry_after": 30
    }
  }
}
```

### Troubleshooting Guide

#### 1. Rate Limiting Issues
```javascript
// Implement exponential backoff
const delay = Math.min(1000 * Math.pow(2, attempt), 30000);
await new Promise(resolve => setTimeout(resolve, delay));
```

#### 2. CORS Issues
```javascript
// Use proxy server or add proper headers
const proxyUrl = 'http://localhost:3001/api/pump/';
const targetUrl = 'https://frontend-api-v3.pump.fun/coins';
const response = await fetch(proxyUrl + encodeURIComponent(targetUrl));
```

#### 3. Data Validation
```javascript
function sanitizeTokenData(token) {
  return {
    ...token,
    usd_market_cap: Math.max(0, parseFloat(token.usd_market_cap) || 0),
    volume: Math.max(0, parseFloat(token.volume) || 0),
    num_holders: Math.max(0, parseInt(token.num_holders) || 0),
    bonding_progress: Math.max(0, Math.min(100, parseFloat(token.bonding_progress) || 0))
  };
}
```

## Performance Optimization

### Caching Strategy
```javascript
// Memory cache with TTL
const cache = new Map();
const CACHE_TTL = 60000; // 1 minute

function getCachedData(key) {
  const cached = cache.get(key);
  if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
    return cached.data;
  }
  cache.delete(key);
  return null;
}

function setCachedData(key, data) {
  cache.set(key, {
    data,
    timestamp: Date.now()
  });
}
```

### Batch Processing
```javascript
// Process tokens in batches to avoid overwhelming APIs
async function processBatch(tokens, batchSize = 10) {
  const results = [];

  for (let i = 0; i < tokens.length; i += batchSize) {
    const batch = tokens.slice(i, i + batchSize);
    const batchResults = await Promise.allSettled(
      batch.map(token => enhanceTokenData(token))
    );

    results.push(...batchResults);

    // Delay between batches
    if (i + batchSize < tokens.length) {
      await sleep(1000);
    }
  }

  return results;
}
```

## Security Considerations

### API Key Management
```bash
# Never commit API keys to version control
# Use environment variables
MORALIS_API_KEY=your_key_here

# Rotate keys regularly
# Monitor usage and set alerts
```

### Input Validation
```javascript
function validateMintAddress(mint) {
  // Solana address validation
  const base58Regex = /^[1-9A-HJ-NP-Za-km-z]{32,44}$/;
  return base58Regex.test(mint);
}

function sanitizeInput(input) {
  return input.replace(/[<>\"'&]/g, '');
}
```

### Rate Limiting Protection
```javascript
class RateLimiter {
  constructor(maxRequests, windowMs) {
    this.maxRequests = maxRequests;
    this.windowMs = windowMs;
    this.requests = [];
  }

  async checkLimit() {
    const now = Date.now();
    this.requests = this.requests.filter(time => now - time < this.windowMs);

    if (this.requests.length >= this.maxRequests) {
      const oldestRequest = Math.min(...this.requests);
      const waitTime = this.windowMs - (now - oldestRequest);
      await sleep(waitTime);
    }

    this.requests.push(now);
  }
}
```

---

**Last Updated:** January 2025
**Version:** 2.0
**Maintained by:** Chad GPT Development Team
