const express = require('express');
const cors = require('cors');
const fetch = require('node-fetch');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 3003;

// Enable CORS for all routes
app.use(cors());
app.use(express.json());

// Serve static files (including our HTML tester)
app.use(express.static('.'));

// Proxy endpoint for pump.fun APIs
app.all('/api/proxy', async (req, res) => {
    try {
        // Extract the target URL from query parameter
        const targetUrl = req.query.url;

        // Validate URL
        if (!targetUrl || !targetUrl.startsWith('http')) {
            return res.status(400).json({ error: 'Invalid URL provided. Use ?url=https://...' });
        }

        console.log(`Proxying request to: ${targetUrl}`);
        console.log(`Method: ${req.method}`);
        console.log(`Query params:`, req.query);

        // Build the full URL with query parameters (excluding the 'url' param)
        const url = new URL(targetUrl);
        Object.keys(req.query).forEach(key => {
            if (key !== 'url') {  // Skip the 'url' parameter itself
                url.searchParams.append(key, req.query[key]);
            }
        });

        // Prepare headers
        const headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'application/json',
            'Origin': 'https://pump.fun',
            'Referer': 'https://pump.fun/',
            'Cache-Control': 'no-cache'
        };

        // Add API key for Moralis requests
        if (req.headers['x-api-key']) {
            headers['X-API-Key'] = req.headers['x-api-key'];
        }

        // Add content-type for POST requests
        if (req.method === 'POST' && req.body) {
            headers['Content-Type'] = 'application/json';
        }

        // Make the request
        const fetchOptions = {
            method: req.method,
            headers: headers,
            timeout: 15000
        };

        // Add body for POST requests
        if (req.method === 'POST' && req.body) {
            fetchOptions.body = JSON.stringify(req.body);
        }

        const response = await fetch(url.toString(), fetchOptions);

        // Get response text first to handle potential JSON parsing issues
        const responseText = await response.text();

        // Log response info
        console.log(`Response status: ${response.status}`);
        console.log(`Response size: ${responseText.length} characters`);

        // Handle empty responses
        if (!responseText || responseText.trim() === '') {
            console.log(`Empty response from: ${url.toString()}`);
            data = {
                error: 'Empty response',
                message: 'API returned empty response',
                status: response.status,
                url: url.toString()
            };
        } else {
            // Try to parse as JSON, fallback to text if it fails
            try {
                data = JSON.parse(responseText);
            } catch (jsonError) {
                console.log(`JSON parse error: ${jsonError.message}`);
                console.log(`Raw response preview: ${responseText.substring(0, 200)}...`);
                // If it's not JSON, return the text wrapped in an object
                data = {
                    error: 'Invalid JSON response',
                    raw_response: responseText,
                    content_type: response.headers.get('content-type'),
                    parse_error: jsonError.message
                };
            }
        }

        // Return the response with proper status
        res.status(response.status).json(data);

    } catch (error) {
        console.error('Proxy error:', error.message);
        
        // Handle different types of errors
        if (error.code === 'ENOTFOUND') {
            res.status(404).json({ 
                error: 'API endpoint not found',
                message: 'The requested API endpoint could not be reached'
            });
        } else if (error.name === 'AbortError') {
            res.status(408).json({ 
                error: 'Request timeout',
                message: 'The API request took too long to respond'
            });
        } else {
            res.status(500).json({ 
                error: 'Proxy server error',
                message: error.message
            });
        }
    }
});

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({ 
        status: 'ok', 
        server: 'CORS Proxy Server',
        port: PORT,
        timestamp: new Date().toISOString()
    });
});

// Serve favicon
app.get('/favicon.ico', (req, res) => {
    res.status(204).end(); // No content
});

// Serve the API tester at root
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'api-tester.html'));
});

// Start the server
app.listen(PORT, () => {
    console.log(`🚀 CORS Proxy Server running on http://localhost:${PORT}`);
    console.log(`📊 API Tester available at http://localhost:${PORT}`);
    console.log(`🔗 Proxy endpoint: http://localhost:${PORT}/api/proxy/[URL]`);
    console.log(`💚 Health check: http://localhost:${PORT}/health`);
});

// Handle graceful shutdown
process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down CORS Proxy Server...');
    process.exit(0);
});

module.exports = app;
