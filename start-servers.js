const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 Starting API Testing Environment...\n');

// Start CORS Proxy Server (port 3003)
console.log('📡 Starting CORS Proxy Server on port 3003...');
const corsProxy = spawn('node', ['cors-proxy-server.js'], {
    stdio: 'pipe',
    cwd: __dirname
});

corsProxy.stdout.on('data', (data) => {
    console.log(`[CORS Proxy] ${data.toString().trim()}`);
});

corsProxy.stderr.on('data', (data) => {
    console.error(`[CORS Proxy Error] ${data.toString().trim()}`);
});

// Start Market Cache Server (port 3004)
console.log('📊 Starting Market Cache Server on port 3004...');
const marketCache = spawn('node', ['market-cache-server.js'], {
    stdio: 'pipe',
    cwd: __dirname
});

marketCache.stdout.on('data', (data) => {
    console.log(`[Market Cache] ${data.toString().trim()}`);
});

marketCache.stderr.on('data', (data) => {
    console.error(`[Market Cache Error] ${data.toString().trim()}`);
});

// Handle process termination
process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down servers...');
    corsProxy.kill('SIGINT');
    marketCache.kill('SIGINT');
    process.exit(0);
});

// Handle server exits
corsProxy.on('exit', (code) => {
    console.log(`[CORS Proxy] Exited with code ${code}`);
});

marketCache.on('exit', (code) => {
    console.log(`[Market Cache] Exited with code ${code}`);
});

console.log('\n✅ Both servers starting...');
console.log('🌐 API Tester: http://localhost:3003');
console.log('📡 CORS Proxy: http://localhost:3003/health');
console.log('📊 Market Cache: http://localhost:3004/health');
console.log('\nPress Ctrl+C to stop all servers');
