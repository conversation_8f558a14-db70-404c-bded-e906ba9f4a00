<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pump.fun API Tester</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
            background: #000000;
            color: #ffffff;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 20px;
            background: #111111;
            border-radius: 12px;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            color: #22c55e;
        }

        .header p {
            color: #888888;
            font-size: 1.1rem;
        }

        .api-section {
            background: #111111;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #181818;
        }

        .api-section h2 {
            color: #22c55e;
            margin-bottom: 15px;
            font-size: 1.5rem;
        }

        .endpoint-group {
            margin-bottom: 30px;
        }

        .endpoint-group h3 {
            color: #ffffff;
            margin-bottom: 15px;
            font-size: 1.2rem;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #888888;
            font-weight: 500;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 10px;
            background: #222222;
            border: 1px solid #333333;
            border-radius: 8px;
            color: #ffffff;
            font-size: 14px;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #22c55e;
            box-shadow: 0 0 0 2px rgba(34, 197, 94, 0.2);
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .btn {
            background: #22c55e;
            color: #000000;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            font-size: 14px;
            transition: all 0.2s;
        }

        .btn:hover {
            background: #16a34a;
            transform: translateY(-1px);
        }

        .btn:active {
            transform: translateY(0);
        }

        .btn-secondary {
            background: #333333;
            color: #ffffff;
        }

        .btn-secondary:hover {
            background: #444444;
        }

        .response-container {
            margin-top: 20px;
        }

        .response-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .response-header h4 {
            color: #22c55e;
        }

        .response-box {
            background: #0a0a0a;
            border: 1px solid #333333;
            border-radius: 8px;
            padding: 15px;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }

        .loading {
            color: #22c55e;
            font-style: italic;
        }

        .error {
            color: #ef4444;
        }

        .success {
            color: #22c55e;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
        }

        .status-200 {
            background: rgba(34, 197, 94, 0.2);
            color: #22c55e;
        }

        .status-error {
            background: rgba(239, 68, 68, 0.2);
            color: #ef4444;
        }

        .copy-btn {
            background: #333333;
            color: #ffffff;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }

        .copy-btn:hover {
            background: #444444;
        }

        .tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 1px solid #333333;
        }

        .tab {
            padding: 12px 20px;
            background: none;
            border: none;
            color: #888888;
            cursor: pointer;
            font-size: 14px;
            border-bottom: 2px solid transparent;
        }

        .tab.active {
            color: #22c55e;
            border-bottom-color: #22c55e;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .form-row {
                grid-template-columns: 1fr;
            }
        }

        /* Modal Styles */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            z-index: 10000;
            display: flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(4px);
        }

        .modal-content {
            background: #111;
            border-radius: 12px;
            width: 90%;
            height: 85vh;
            max-width: none;
            max-height: none;
            display: flex;
            flex-direction: column;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
            border: 1px solid #333;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            border-bottom: 1px solid #333;
            background: #0a0a0a;
            border-radius: 12px 12px 0 0;
            flex-shrink: 0;
        }

        .modal-header h3 {
            margin: 0;
            color: #22c55e;
            font-size: 18px;
        }

        .modal-controls {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .close-btn {
            background: none;
            border: none;
            color: #888;
            font-size: 24px;
            cursor: pointer;
            padding: 5px 10px;
            border-radius: 4px;
            transition: all 0.2s;
        }

        .close-btn:hover {
            background: #333;
            color: #fff;
        }

        .modal-body {
            padding: 0;
            flex: 1;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            min-height: 0; /* Important for flex child to shrink */
        }

        .modal .response-box {
            flex: 1;
            margin: 0;
            border-radius: 0 0 12px 12px;
            border: none;
            overflow-y: auto;
            height: 100%;
            min-height: 0; /* Important for flex child to shrink */
            display: flex;
            flex-direction: column;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Pump.fun API Tester</h1>
            <p>Test all pump.fun API endpoints with a beautiful interface</p>
            <div id="proxy-status" style="margin-top: 15px; padding: 10px; border-radius: 8px; font-size: 14px;">
                <span id="status-indicator">🔄</span> <span id="status-text">Checking proxy server...</span>
            </div>
        </div>

        <div class="tabs">
            <button class="tab active" onclick="switchTab('pumpfun-api')">PumpFun API</button>
            <button class="tab" onclick="switchTab('moralis')">Moralis API</button>
            <button class="tab" onclick="switchTab('market-cache')">Market Cache</button>
            <button class="tab" onclick="switchTab('secrets')">Secrets</button>
        </div>

        <!-- PumpFun API Tab -->
        <div id="pumpfun-api" class="tab-content active">
            <div style="text-align: center; margin-bottom: 30px; padding: 20px;">
                <h2>PumpFun API</h2>
                <p style="color: #888;">Complete pump.fun API suite (Frontend v3, Advanced v2, Legacy)</p>
            </div>





                <!-- Frontend API v3 Section -->
                <div style="background: #111; border: 1px solid #222; border-radius: 12px; padding: 20px; margin-bottom: 25px;">
                    <h3 style="color: #22c55e; margin-bottom: 15px; border-bottom: 1px solid #333; padding-bottom: 10px;">
                        Frontend API v3
                    </h3>
                    <p style="color: #888; font-size: 14px; margin-bottom: 20px;">Base URL: https://frontend-api-v3.pump.fun</p>

                    <div class="endpoint-group">
                        <h3>Get Individual Token</h3>
                        <div class="form-group">
                            <label>Mint Address</label>
                            <input type="text" id="v3-mint" placeholder="9BB6NFEcjBCtnNLFko2FqVQBq8HHM13kCyYcdQbgpump">
                        </div>
                        <button class="btn" onclick="testV3Token()">Test Get Token</button>
                    </div>

                    <div class="endpoint-group">
                        <h3>Get For You Recommendations</h3>
                        <div class="form-row">
                            <div class="form-group">
                                <label>Limit</label>
                                <input type="number" id="v3-foryou-limit" value="48" min="1" max="100">
                            </div>
                            <div class="form-group">
                                <label>Offset</label>
                                <input type="number" id="v3-foryou-offset" value="0" min="0">
                            </div>
                            <div class="form-group">
                                <label>Include NSFW</label>
                                <select id="v3-foryou-nsfw">
                                    <option value="false">No</option>
                                    <option value="true">Yes</option>
                                </select>
                            </div>
                        </div>
                        <button class="btn" onclick="testV3ForYou()">Test For You</button>
                    </div>

                    <div class="endpoint-group">
                        <h3>Get Now-Trending (Experimental)</h3>
                        <div class="form-row">
                            <div class="form-group">
                                <label>Limit</label>
                                <input type="number" id="v3-trending-limit" value="48" min="1" max="100">
                            </div>
                            <div class="form-group">
                                <label>Offset</label>
                                <input type="number" id="v3-trending-offset" value="0" min="0">
                            </div>
                            <div class="form-group">
                                <label>Include NSFW</label>
                                <select id="v3-trending-nsfw">
                                    <option value="false">No</option>
                                    <option value="true">Yes</option>
                                </select>
                            </div>
                        </div>
                        <button class="btn" onclick="testV3NowTrending()">Test Now-Trending</button>
                        <p style="color: #666; font-size: 12px; margin-top: 10px;">
                            🧪 <strong>Experimental:</strong> Testing if /coins/now-trending endpoint exists (same pattern as for-you)
                        </p>
                    </div>

                    <div class="endpoint-group">
                        <h3>Trading Data</h3>
                        <div class="form-group">
                            <label>Mint Address</label>
                            <input type="text" id="v3-trading-mint" placeholder="9BB6NFEcjBCtnNLFko2FqVQBq8HHM13kCyYcdQbgpump">
                        </div>
                        <div class="form-row">
                            <button class="btn" onclick="testV3Trades()">Get Trades</button>
                            <button class="btn" onclick="testV3Holders()">Get Holders</button>
                            <button class="btn" onclick="testV3Volume()">Get Volume</button>
                        </div>
                    </div>
                </div>

                
                <!-- Advanced API v2 Section -->
                <div style="background: #111; border: 1px solid #222; border-radius: 12px; padding: 20px; margin-bottom: 25px;">
                    <h3 style="color: #22c55e; margin-bottom: 15px; border-bottom: 1px solid #333; padding-bottom: 10px;">
                        Advanced API v2
                    </h3>
                    <p style="color: #888; font-size: 14px; margin-bottom: 20px;">Base URL: https://advanced-api-v2.pump.fun</p>

                    <div class="endpoint-group">
                        <h3>Enhanced Coins List</h3>
                        <div class="form-row">
                            <div class="form-group">
                                <label>Sort By</label>
                                <select id="v2-sort">
                                    <option value="marketCap">Market Cap</option>
                                    <option value="volume">Volume</option>
                                    <option value="createdAt">Created At</option>
                                    <option value="lastTradeAt">Last Trade At</option>
                                    <option value="featured">Featured</option>
                                    <option value="trending">Trending (Experimental)</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Limit</label>
                                <input type="number" id="v2-limit" value="50" min="1" max="100">
                            </div>
                            <div class="form-group">
                                <label>Offset</label>
                                <input type="number" id="v2-offset" value="0" min="0">
                            </div>
                        </div>
                        <button class="btn" onclick="testV2Coins()">Test Enhanced Coins</button>
                    </div>

                    <div class="endpoint-group">
                        <h3>Graduated Tokens</h3>
                        <div class="form-row">
                            <div class="form-group">
                                <label>Limit</label>
                                <input type="number" id="v2-grad-limit" value="50" min="1" max="100">
                            </div>
                            <div class="form-group">
                                <label>Offset</label>
                                <input type="number" id="v2-grad-offset" value="0" min="0">
                            </div>
                        </div>
                        <button class="btn" onclick="testV2Graduated()">Test Enhanced Graduated</button>
                    </div>
                </div>

                <!-- Legacy API Section -->
                <div style="background: #111; border: 1px solid #222; border-radius: 12px; padding: 20px; margin-bottom: 25px;">
                    <h3 style="color: #22c55e; margin-bottom: 15px; border-bottom: 1px solid #333; padding-bottom: 10px;">
                        Legacy API
                    </h3>
                    <p style="color: #888; font-size: 14px; margin-bottom: 20px;">Base URL: https://pump.fun/api</p>

                    <div class="endpoint-group">
                        <h3>Token Details</h3>
                        <div class="form-group">
                            <label>Mint Address</label>
                            <input type="text" id="legacy-mint" placeholder="9BB6NFEcjBCtnNLFko2FqVQBq8HHM13kCyYcdQbgpump">
                        </div>
                        <button class="btn" onclick="testLegacyToken()">Test Legacy Token</button>
                    </div>

                    <div class="endpoint-group">
                        <h3>Runners (Trending)</h3>
                        <p style="color: #888; font-size: 14px; margin-bottom: 15px;">Returns trending/running tokens with descriptions and metadata</p>
                        <button class="btn" onclick="testLegacyRunners()">Test Get Runners</button>
                    </div>

                    <div class="endpoint-group">
                        <h3>Flags/Categories</h3>
                        <p style="color: #888; font-size: 14px; margin-bottom: 15px;">Returns available flags and categories</p>
                        <button class="btn" onclick="testLegacyFlags()">Test Get Flags</button>
                    </div>
                </div>
        </div>



        <!-- Moralis API Tab -->
        <div id="moralis" class="tab-content">
            <div style="text-align: center; margin-bottom: 30px; padding: 20px;">
                <h2>Moralis API</h2>
                <p style="color: #888;">Comprehensive Solana & Pump.fun token data</p>
            </div>

            <!-- Pump Fun Tokens Section -->
            <div style="background: #111; border: 1px solid #222; border-radius: 12px; padding: 20px; margin-bottom: 25px;">
                <h3 style="color: #22c55e; margin-bottom: 15px; border-bottom: 1px solid #333; padding-bottom: 10px;">
                    Pump Fun Tokens
                </h3>
                <p style="color: #888; font-size: 14px; margin-bottom: 20px;">
                    Access comprehensive data on pump fun tokens and prebonded tokens. Monitor new launches, track bonding phase tokens, and analyze graduated tokens.
                </p>

                <!-- API Key Input (shared across all endpoints) -->
                <div class="endpoint-group">
                    <h3>API Configuration</h3>
                    <div class="form-group">
                        <label>Moralis API Key</label>
                        <input type="password" id="moralis-key" placeholder="Your Moralis API Key" style="width: 100%;">
                    </div>
                </div>

                <!-- New Tokens -->
                <div class="endpoint-group">
                    <h3>Get New Tokens by Exchange</h3>
                    <p style="color: #888; font-size: 14px; margin-bottom: 15px;">Monitor new token launches on pump.fun</p>
                    <div class="form-row">
                        <div class="form-group">
                            <label>Exchange</label>
                            <select id="moralis-new-exchange">
                                <option value="pumpfun">Pump.fun</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Limit</label>
                            <input type="number" id="moralis-new-limit" value="20" min="1" max="100">
                        </div>
                    </div>
                    <button class="btn" onclick="testMoralisNewTokens()">Get New Tokens</button>
                </div>

                <!-- Bonding Tokens -->
                <div class="endpoint-group">
                    <h3>Get Bonding Tokens by Exchange</h3>
                    <p style="color: #888; font-size: 14px; margin-bottom: 15px;">Track tokens currently in bonding phase</p>
                    <div class="form-row">
                        <div class="form-group">
                            <label>Exchange</label>
                            <select id="moralis-bonding-exchange">
                                <option value="pumpfun">Pump.fun</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Limit</label>
                            <input type="number" id="moralis-bonding-limit" value="20" min="1" max="100">
                        </div>
                    </div>
                    <button class="btn" onclick="testMoralisBondingTokens()">Get Bonding Tokens</button>
                </div>

                <!-- Graduated Tokens -->
                <div class="endpoint-group">
                    <h3>Get Graduated Tokens by Exchange</h3>
                    <p style="color: #888; font-size: 14px; margin-bottom: 15px;">Analyze tokens that completed bonding curve</p>
                    <div class="form-row">
                        <div class="form-group">
                            <label>Exchange</label>
                            <select id="moralis-graduated-exchange">
                                <option value="pumpfun">Pump.fun</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Limit</label>
                            <input type="number" id="moralis-graduated-limit" value="20" min="1" max="100">
                        </div>
                    </div>
                    <button class="btn" onclick="testMoralisGraduatedTokens()">Get Graduated Tokens</button>
                </div>

                <!-- Bonding Status -->
                <div class="endpoint-group">
                    <h3>Get Token Bonding Status</h3>
                    <p style="color: #888; font-size: 14px; margin-bottom: 15px;">Verify bonding status by token address</p>
                    <div class="form-group">
                        <label>Token Address</label>
                        <input type="text" id="moralis-status-token" placeholder="H2p8S7Ssd3mrBft1bcDGnzW8KNRAGtPTtJLv1tnupump">
                    </div>
                    <button class="btn" onclick="testMoralisBondingStatus()">Get Bonding Status</button>
                </div>
            </div>
        </div>

        <!-- Market Cache Server Tab -->
        <div id="market-cache" class="tab-content">
            <div class="api-section">
                <h2>Market Cache Server</h2>
                <p style="color: #888; margin-bottom: 20px;">Base URL: http://localhost:3004 | Aggregates pump.fun data into market categories</p>

                <div class="endpoint-group">
                    <h3>Available Data</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label>Limit</label>
                            <input type="number" id="market-limit" value="50" min="1" max="100">
                        </div>
                        <div class="form-group">
                            <label>Offset</label>
                            <input type="number" id="market-offset" value="0" min="0">
                        </div>
                    </div>
                    <div class="form-row">
                        <button class="btn" onclick="testMarketForYou()">Get For You</button>
                        <button class="btn btn-secondary" onclick="testMarketAll()">Get All Available</button>
                    </div>
                    <div style="background: #111; border: 1px solid #333; border-radius: 8px; padding: 15px; margin-top: 15px;">
                        <p style="color: #888; font-size: 14px; line-height: 1.6; margin: 0;">
                            📝 <strong>Note:</strong> Frontend API v3 /coins endpoint doesn't work.<br>
                            • <strong>For You recommendations</strong> → Available here ✅<br>
                            • <strong>Coins lists (trending, new, volume)</strong> → Use <strong>PumpFun API</strong> tab<br>
                            • <strong>Graduated tokens</strong> → Use <strong>PumpFun API</strong> tab
                        </p>
                    </div>
                </div>

                <div class="endpoint-group">
                    <h3>Server Management</h3>
                    <div class="form-row">
                        <button class="btn" onclick="testMarketStatus()">Server Status</button>
                        <button class="btn btn-secondary" onclick="testMarketRefresh()">Force Refresh</button>
                    </div>
                </div>

                <div class="response-container">
                    <div class="response-header">
                        <h4>Response</h4>
                        <button class="copy-btn" onclick="copyResponse('market-response')">Copy</button>
                    </div>
                    <div id="market-response" class="response-box">Click a button above to test an endpoint...</div>
                </div>
            </div>
        </div>






        <!-- Secrets Tab -->
        <div id="secrets" class="tab-content">
            <div style="text-align: center; margin-bottom: 30px; padding: 20px;">
                <h2>🔐 Secrets Management</h2>
                <p style="color: #888;">Securely store and manage your API keys</p>
            </div>

            <!-- API Keys Section -->
            <div style="background: #111; border: 1px solid #222; border-radius: 12px; padding: 20px; margin-bottom: 25px;">
                <h3 style="color: #22c55e; margin-bottom: 15px; border-bottom: 1px solid #333; padding-bottom: 10px;">
                    API Keys
                </h3>
                <p style="color: #888; font-size: 14px; margin-bottom: 20px;">
                    Store your API keys securely in browser localStorage. Keys are encrypted and only accessible on this device.
                </p>

                <!-- Moralis API Key -->
                <div class="endpoint-group">
                    <h3>Moralis API Key</h3>
                    <div class="form-row">
                        <div class="form-group" style="flex: 1;">
                            <label>API Key</label>
                            <input type="password" id="secret-moralis-key" placeholder="Enter your Moralis API key">
                        </div>
                        <div style="display: flex; gap: 10px; align-items: end;">
                            <button class="btn" onclick="saveSecret('MORALIS_API_KEY', 'secret-moralis-key')">Save</button>
                            <button class="btn btn-secondary" onclick="loadSecret('MORALIS_API_KEY', 'secret-moralis-key')">Load</button>
                            <button class="btn" style="background: #ef4444;" onclick="deleteSecret('MORALIS_API_KEY')">Delete</button>
                        </div>
                    </div>
                    <div id="moralis-status" style="margin-top: 10px; font-size: 12px;"></div>
                </div>

                <!-- Add New Secret -->
                <div class="endpoint-group">
                    <h3>Add Custom Secret</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label>Secret Name</label>
                            <input type="text" id="custom-secret-name" placeholder="e.g., CUSTOM_API_KEY">
                        </div>
                        <div class="form-group">
                            <label>Secret Value</label>
                            <input type="password" id="custom-secret-value" placeholder="Enter secret value">
                        </div>
                        <div style="display: flex; align-items: end;">
                            <button class="btn" onclick="saveCustomSecret()">Add Secret</button>
                        </div>
                    </div>
                </div>

                <!-- Stored Secrets List -->
                <div class="endpoint-group">
                    <h3>Stored Secrets</h3>
                    <div id="secrets-list" style="background: #0a0a0a; border: 1px solid #333; border-radius: 8px; padding: 15px; min-height: 100px;">
                        <div style="color: #666; text-align: center;">No secrets stored</div>
                    </div>
                    <div style="margin-top: 10px;">
                        <button class="btn btn-secondary" onclick="refreshSecretsList()">Refresh List</button>
                        <button class="btn" style="background: #ef4444; margin-left: 10px;" onclick="clearAllSecrets()">Clear All</button>
                    </div>
                </div>

                <!-- Security Notice -->
                <div style="background: #1a1a0a; border: 1px solid #444; border-radius: 8px; padding: 15px; margin-top: 20px;">
                    <h4 style="color: #f59e0b; margin-bottom: 10px;">🔒 Security Notice</h4>
                    <ul style="color: #888; font-size: 14px; line-height: 1.6; margin: 0; padding-left: 20px;">
                        <li>Secrets are stored in browser localStorage (device-specific)</li>
                        <li>Keys are base64 encoded but not encrypted - use only for testing</li>
                        <li>Never share your API keys or use production keys in testing tools</li>
                        <li>Clear secrets when using shared/public computers</li>
                    </ul>
                </div>
            </div>
        </div>

    </div>

    <!-- Response Modal -->
    <div id="responseModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">API Response</h3>
                <div class="modal-controls">
                    <button class="copy-btn" onclick="copyModalResponse()">Copy</button>
                    <button class="close-btn" onclick="closeModal()">&times;</button>
                </div>
            </div>
            <div class="modal-body">
                <div id="modalResponse" class="response-box" style="display: flex; flex-direction: column; height: 100%;"></div>
            </div>
        </div>
    </div>

    <script>
        // Check proxy server status on page load
        async function checkProxyStatus() {
            const statusIndicator = document.getElementById('status-indicator');
            const statusText = document.getElementById('status-text');
            const statusDiv = document.getElementById('proxy-status');

            try {
                const response = await fetch('http://localhost:3003/health');
                if (response.ok) {
                    statusIndicator.textContent = '✅';
                    statusText.textContent = 'Proxy server running - CORS issues resolved!';
                    statusDiv.style.background = 'rgba(34, 197, 94, 0.2)';
                    statusDiv.style.border = '1px solid #22c55e';
                    statusDiv.style.color = '#22c55e';
                } else {
                    throw new Error('Server not responding');
                }
            } catch (error) {
                statusIndicator.textContent = '⚠️';
                statusText.innerHTML = 'Proxy server not running. <a href="#setup" style="color: #22c55e;">See setup instructions below</a>';
                statusDiv.style.background = 'rgba(239, 68, 68, 0.2)';
                statusDiv.style.border = '1px solid #ef4444';
                statusDiv.style.color = '#ef4444';
            }
        }

        // Tab switching functionality
        function switchTab(tabName) {
            // Hide all tab contents
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => content.classList.remove('active'));

            // Remove active class from all tabs
            const tabs = document.querySelectorAll('.tab');
            tabs.forEach(tab => tab.classList.remove('active'));

            // Show selected tab content
            document.getElementById(tabName).classList.add('active');

            // Add active class to clicked tab
            event.target.classList.add('active');
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', () => {
            checkProxyStatus();
        });

        // Utility functions
        function displayResponse(data, status = null, title = 'API Response') {
            showModal(title);
            const element = document.getElementById('modalResponse');
            const timestamp = new Date().toLocaleTimeString();

            let statusInfo = '';
            if (status) {
                const statusColor = status >= 200 && status < 300 ? '#22c55e' : '#ef4444';
                statusInfo = `<strong>Status:</strong> <span style="color: ${statusColor};">HTTP ${status}</span> | `;
            }

            const responseSize = typeof data === 'object' ? JSON.stringify(data).length : data.length;
            const responseText = typeof data === 'object' ? JSON.stringify(data, null, 2) : data;

            element.innerHTML = `
                <div style="color: #888; padding: 10px 15px; background: #0a0a0a; border-bottom: 1px solid #333; font-size: 12px; flex-shrink: 0;">
                    ${statusInfo}<strong>Size:</strong> ${responseSize} characters | <strong>Time:</strong> ${timestamp}
                </div>
                <div style="padding: 15px; flex: 1; overflow-y: auto; min-height: 0;">
                    <pre style="white-space: pre-wrap; word-wrap: break-word; margin: 0; font-family: 'Courier New', monospace; font-size: 12px; line-height: 1.3; color: #22c55e;">${responseText}</pre>
                </div>
            `;
        }

        function displayError(message, title = 'Error') {
            showModal(title);
            const element = document.getElementById('modalResponse');
            const timestamp = new Date().toLocaleTimeString();
            element.innerHTML = `
                <div style="color: #ef4444; padding: 20px; text-align: center; font-size: 16px; height: 100%; display: flex; flex-direction: column; justify-content: center;">
                    <div style="font-size: 48px; margin-bottom: 20px;">❌</div>
                    <div style="margin-bottom: 15px;"><strong>Error occurred at ${timestamp}</strong></div>
                    <div style="background: #1a0a0a; border: 1px solid #333; border-radius: 8px; padding: 15px; text-align: left; flex: 1; overflow-y: auto;">
                        <pre style="margin: 0; white-space: pre-wrap; word-wrap: break-word; color: #22c55e; font-size: 12px; line-height: 1.3;">${message}</pre>
                    </div>
                </div>
            `;
        }

        function displayLoading(title = 'Loading...') {
            showModal(title);
            const element = document.getElementById('modalResponse');
            element.innerHTML = '<div style="text-align: center; padding: 20px; color: #888; font-size: 16px; height: 100%; display: flex; align-items: center; justify-content: center;">🔄 Loading...</div>';
        }

        function showModal(title) {
            document.getElementById('modalTitle').textContent = title;
            document.getElementById('responseModal').style.display = 'flex';
            document.body.style.overflow = 'hidden';
        }

        function closeModal() {
            document.getElementById('responseModal').style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        function copyModalResponse() {
            const responseElement = document.getElementById('modalResponse');
            const preElement = responseElement.querySelector('pre');
            if (preElement) {
                navigator.clipboard.writeText(preElement.textContent);

                // Visual feedback
                const copyBtn = document.querySelector('.modal .copy-btn');
                const originalText = copyBtn.textContent;
                copyBtn.textContent = 'Copied!';
                copyBtn.style.background = '#22c55e';

                setTimeout(() => {
                    copyBtn.textContent = originalText;
                    copyBtn.style.background = '';
                }, 2000);
            }
        }

        // Close modal when clicking outside
        document.addEventListener('click', function(event) {
            const modal = document.getElementById('responseModal');
            if (event.target === modal) {
                closeModal();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeModal();
            }
        });

        // Configuration
        const USE_PROXY = true; // Set to false to try direct requests
        const PROXY_BASE = 'http://localhost:3003/api/proxy';

        // API testing functions
        async function makeRequest(url, options = {}) {
            try {
                let requestUrl = url;
                let requestOptions = { ...options };

                // Use proxy for external APIs to avoid CORS issues
                if (USE_PROXY && !url.startsWith('http://localhost')) {
                    // Parse the original URL to extract query parameters
                    const urlObj = new URL(url);
                    const proxyUrl = new URL(PROXY_BASE);

                    // Add the base URL as the 'url' parameter
                    proxyUrl.searchParams.set('url', urlObj.origin + urlObj.pathname);

                    // Add all original query parameters
                    urlObj.searchParams.forEach((value, key) => {
                        proxyUrl.searchParams.set(key, value);
                    });

                    requestUrl = proxyUrl.toString();

                    // Move API key to header for Moralis requests
                    if (options.headers && options.headers['X-API-Key']) {
                        requestOptions.headers = {
                            ...options.headers,
                            'x-api-key': options.headers['X-API-Key']
                        };
                        delete requestOptions.headers['X-API-Key'];
                    }
                }

                const response = await fetch(requestUrl, {
                    ...requestOptions,
                    headers: {
                        'Accept': 'application/json',
                        'Cache-Control': 'no-cache',
                        ...requestOptions.headers
                    }
                });

                const data = await response.json();
                return { data, status: response.status };
            } catch (error) {
                throw new Error(`Request failed: ${error.message}`);
            }
        }

        // Frontend API v3 functions



        async function testV3Token() {
            displayLoading('Frontend API v3 - Individual Token');

            const mint = document.getElementById('v3-mint').value;
            if (!mint) {
                displayError('Please enter a mint address', 'Input Required');
                return;
            }

            const url = `https://frontend-api-v3.pump.fun/coins/${mint}`;

            try {
                const { data, status } = await makeRequest(url);
                displayResponse(data, status, `Token: ${mint.substring(0, 8)}...`);
            } catch (error) {
                displayError(error.message, 'Frontend API v3 Error');
            }
        }

        async function testV3ForYou() {
            displayLoading('Frontend API v3 - For You Recommendations');

            const limit = document.getElementById('v3-foryou-limit').value;
            const offset = document.getElementById('v3-foryou-offset').value;
            const nsfw = document.getElementById('v3-foryou-nsfw').value;

            const url = `https://frontend-api-v3.pump.fun/coins/for-you?offset=${offset}&limit=${limit}&includeNsfw=${nsfw}`;

            try {
                const { data, status } = await makeRequest(url);
                displayResponse({
                    success: true,
                    endpoint: '/coins/for-you',
                    description: 'Pump.fun algorithmic recommendations',
                    count: data?.length || 0,
                    data: data
                }, status, `For You (${data?.length || 0} tokens)`);
            } catch (error) {
                displayError(error.message, 'For You API Error');
            }
        }

        async function testV3NowTrending() {
            displayLoading('pumpfun-response');

            const limit = document.getElementById('v3-trending-limit').value;
            const offset = document.getElementById('v3-trending-offset').value;
            const nsfw = document.getElementById('v3-trending-nsfw').value;

            const url = `https://frontend-api-v3.pump.fun/coins/now-trending?offset=${offset}&limit=${limit}&includeNsfw=${nsfw}`;

            try {
                const { data, status } = await makeRequest(url);
                displayResponse('pumpfun-response', {
                    success: true,
                    endpoint: '/coins/now-trending',
                    description: 'Experimental now-trending tokens endpoint',
                    count: data?.length || 0,
                    data: data,
                    note: 'Testing if now-trending endpoint exists with same pattern as for-you'
                }, status);
            } catch (error) {
                displayError('pumpfun-response', `Now-trending endpoint test failed: ${error.message}`);
            }
        }

        // Trading data functions
        async function testV3Trades() {
            displayLoading('pumpfun-response');

            const mint = document.getElementById('v3-trading-mint').value;
            if (!mint) {
                displayError('pumpfun-response', 'Please enter a mint address');
                return;
            }

            const url = `https://frontend-api-v3.pump.fun/coins/${mint}/trades`;

            try {
                const { data, status } = await makeRequest(url);
                displayResponse('pumpfun-response', data, status);
            } catch (error) {
                displayError('pumpfun-response', error.message);
            }
        }

        async function testV3Holders() {
            displayLoading('pumpfun-response');

            const mint = document.getElementById('v3-trading-mint').value;
            if (!mint) {
                displayError('pumpfun-response', 'Please enter a mint address');
                return;
            }

            const url = `https://frontend-api-v3.pump.fun/coins/${mint}/holders`;

            try {
                const { data, status } = await makeRequest(url);
                displayResponse('pumpfun-response', data, status);
            } catch (error) {
                displayError('pumpfun-response', error.message);
            }
        }

        async function testV3Volume() {
            displayLoading('pumpfun-response');

            const mint = document.getElementById('v3-trading-mint').value;
            if (!mint) {
                displayError('pumpfun-response', 'Please enter a mint address');
                return;
            }

            const url = `https://frontend-api-v3.pump.fun/coins/${mint}/volume`;

            try {
                const { data, status } = await makeRequest(url);
                displayResponse('pumpfun-response', data, status);
            } catch (error) {
                displayError('pumpfun-response', error.message);
            }
        }



        // Market Cache Server functions (only forYou works)

        async function testMarketForYou() {
            displayLoading('market-response');

            const limit = document.getElementById('market-limit').value;
            const offset = document.getElementById('market-offset').value;
            const url = `http://localhost:3004/api/market/for-you?limit=${limit}&offset=${offset}`;

            try {
                const { data, status } = await makeRequest(url);
                displayResponse('market-response', data, status);
            } catch (error) {
                displayError('market-response', `${error.message}. Start server: npm run market-cache`);
            }
        }



        async function testMarketAll() {
            displayLoading('market-response');

            const limit = document.getElementById('market-limit').value;
            const url = `http://localhost:3004/api/market/all?limit=${limit}`;

            try {
                const { data, status } = await makeRequest(url);
                displayResponse('market-response', data, status);
            } catch (error) {
                displayError('market-response', `${error.message}. Start server: npm run market-cache`);
            }
        }

        async function testMarketStatus() {
            displayLoading('market-response');

            const url = 'http://localhost:3004/api/status';

            try {
                const { data, status } = await makeRequest(url);
                displayResponse('market-response', data, status);
            } catch (error) {
                displayError('market-response', `${error.message}. Start server: npm run market-cache`);
            }
        }

        async function testMarketRefresh() {
            displayLoading('market-response');

            const url = 'http://localhost:3004/api/refresh';

            try {
                const { data, status } = await makeRequest(url, { method: 'POST' });
                displayResponse('market-response', data, status);
            } catch (error) {
                displayError('market-response', `${error.message}. Start server: npm run market-cache`);
            }
        }

        // Advanced API v2 functions
        async function testV2Coins() {
            displayLoading('pumpfun-response');

            const sortBy = document.getElementById('v2-sort').value;
            const limit = document.getElementById('v2-limit').value;
            const offset = document.getElementById('v2-offset').value;

            const url = `https://advanced-api-v2.pump.fun/coins/list?sortBy=${sortBy}&limit=${limit}&offset=${offset}`;

            try {
                const { data, status } = await makeRequest(url);
                displayResponse('pumpfun-response', data, status);
            } catch (error) {
                displayError('pumpfun-response', error.message);
            }
        }

        async function testV2Graduated() {
            displayLoading('pumpfun-response');

            const limit = document.getElementById('v2-grad-limit').value;
            const offset = document.getElementById('v2-grad-offset').value;

            const url = `https://advanced-api-v2.pump.fun/coins/graduated?limit=${limit}&offset=${offset}`;

            try {
                const { data, status } = await makeRequest(url);
                displayResponse('pumpfun-response', data, status);
            } catch (error) {
                displayError('pumpfun-response', error.message);
            }
        }

        // Secrets Management functions
        function saveSecret(secretName, inputId) {
            const value = document.getElementById(inputId).value;
            if (!value) {
                showStatus(`${secretName.toLowerCase()}-status`, 'Please enter a value', 'error');
                return;
            }

            // Base64 encode for basic obfuscation (not real encryption)
            const encodedValue = btoa(value);
            localStorage.setItem(`API_TESTER_${secretName}`, encodedValue);

            showStatus(`${secretName.toLowerCase()}-status`, 'Secret saved successfully!', 'success');
            refreshSecretsList();

            // Clear the input for security
            document.getElementById(inputId).value = '';
        }

        function loadSecret(secretName, inputId) {
            const encodedValue = localStorage.getItem(`API_TESTER_${secretName}`);
            if (!encodedValue) {
                showStatus(`${secretName.toLowerCase()}-status`, 'No saved secret found', 'error');
                return;
            }

            try {
                const decodedValue = atob(encodedValue);
                document.getElementById(inputId).value = decodedValue;
                showStatus(`${secretName.toLowerCase()}-status`, 'Secret loaded successfully!', 'success');
            } catch (error) {
                showStatus(`${secretName.toLowerCase()}-status`, 'Error loading secret', 'error');
            }
        }

        function deleteSecret(secretName) {
            localStorage.removeItem(`API_TESTER_${secretName}`);
            showStatus(`${secretName.toLowerCase()}-status`, 'Secret deleted', 'success');
            refreshSecretsList();
        }

        function saveCustomSecret() {
            const name = document.getElementById('custom-secret-name').value;
            const value = document.getElementById('custom-secret-value').value;

            if (!name || !value) {
                alert('Please enter both name and value');
                return;
            }

            const encodedValue = btoa(value);
            localStorage.setItem(`API_TESTER_${name.toUpperCase()}`, encodedValue);

            // Clear inputs
            document.getElementById('custom-secret-name').value = '';
            document.getElementById('custom-secret-value').value = '';

            refreshSecretsList();
        }

        function refreshSecretsList() {
            const listContainer = document.getElementById('secrets-list');
            const secrets = [];

            // Find all API_TESTER_ keys
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && key.startsWith('API_TESTER_')) {
                    const secretName = key.replace('API_TESTER_', '');
                    secrets.push(secretName);
                }
            }

            if (secrets.length === 0) {
                listContainer.innerHTML = '<div style="color: #666; text-align: center;">No secrets stored</div>';
                return;
            }

            const secretsHtml = secrets.map(secret => `
                <div style="display: flex; justify-content: space-between; align-items: center; padding: 10px; border-bottom: 1px solid #333;">
                    <span style="color: #22c55e; font-family: monospace;">${secret}</span>
                    <button class="btn" style="background: #ef4444; padding: 5px 10px; font-size: 12px;" onclick="deleteSecret('${secret}')">Delete</button>
                </div>
            `).join('');

            listContainer.innerHTML = secretsHtml;
        }

        function clearAllSecrets() {
            if (!confirm('Are you sure you want to delete all stored secrets?')) {
                return;
            }

            const keysToDelete = [];
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && key.startsWith('API_TESTER_')) {
                    keysToDelete.push(key);
                }
            }

            keysToDelete.forEach(key => localStorage.removeItem(key));
            refreshSecretsList();
        }

        function showStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            if (!element) return;

            const color = type === 'success' ? '#22c55e' : '#ef4444';
            element.innerHTML = `<span style="color: ${color};">${message}</span>`;

            // Clear after 3 seconds
            setTimeout(() => {
                element.innerHTML = '';
            }, 3000);
        }

        // Auto-save the provided Moralis API key on page load
        document.addEventListener('DOMContentLoaded', function() {
            const moralisKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJub25jZSI6ImI1YmVhMTc1LWU5MmEtNGU0ZS1hOTgxLWU3ZjJlNTk5NjYyMCIsIm9yZ0lkIjoiNDA1NzgzIiwidXNlcklkIjoiNDE2OTY2IiwidHlwZUlkIjoiZTYyZmNjOGMtMTZlMi00ZmY1LWJjMjItNzVlYTBhZTRjNDk2IiwidHlwZSI6IlBST0pFQ1QiLCJpYXQiOjE3MjQ1MTYzOTcsImV4cCI6NDg4MDI3NjM5N30.73TV_J52D11itXTgHvc1wr_kavMbyHAcxCya1TNEoeI';

            // Save the Moralis key if not already saved
            if (!localStorage.getItem('API_TESTER_MORALIS_API_KEY')) {
                localStorage.setItem('API_TESTER_MORALIS_API_KEY', btoa(moralisKey));
            }

            refreshSecretsList();
        });

        // Moralis API functions
        function getMoralisApiKey() {
            // First try to get from secrets storage
            const encodedKey = localStorage.getItem('API_TESTER_MORALIS_API_KEY');
            if (encodedKey) {
                try {
                    return atob(encodedKey);
                } catch (error) {
                    console.error('Error decoding Moralis API key');
                }
            }

            // Fallback to manual input
            const apiKey = document.getElementById('moralis-key').value;
            if (!apiKey) {
                displayError('Please enter your Moralis API key or save it in the Secrets tab', 'API Key Required');
                return null;
            }
            return apiKey;
        }

        async function testMoralisNewTokens() {
            const apiKey = getMoralisApiKey();
            if (!apiKey) return;

            displayLoading('Moralis API - New Tokens');

            const exchange = document.getElementById('moralis-new-exchange').value;
            const limit = document.getElementById('moralis-new-limit').value;

            const url = `https://solana-gateway.moralis.io/token/mainnet/exchange/${exchange}/new?limit=${limit}`;

            try {
                const { data, status } = await makeRequest(url, {
                    headers: {
                        'X-API-Key': apiKey
                    }
                });
                displayResponse(data, status, `New Tokens (${data?.result?.length || 0} tokens)`);
            } catch (error) {
                displayError(error.message, 'Moralis API Error');
            }
        }

        async function testMoralisBondingTokens() {
            const apiKey = getMoralisApiKey();
            if (!apiKey) return;

            displayLoading('Moralis API - Bonding Tokens');

            const exchange = document.getElementById('moralis-bonding-exchange').value;
            const limit = document.getElementById('moralis-bonding-limit').value;

            const url = `https://solana-gateway.moralis.io/token/mainnet/exchange/${exchange}/bonding?limit=${limit}`;

            try {
                const { data, status } = await makeRequest(url, {
                    headers: {
                        'X-API-Key': apiKey
                    }
                });
                displayResponse(data, status, `Bonding Tokens (${data?.result?.length || 0} tokens)`);
            } catch (error) {
                displayError(error.message, 'Moralis API Error');
            }
        }

        async function testMoralisGraduatedTokens() {
            const apiKey = getMoralisApiKey();
            if (!apiKey) return;

            displayLoading('Moralis API - Graduated Tokens');

            const exchange = document.getElementById('moralis-graduated-exchange').value;
            const limit = document.getElementById('moralis-graduated-limit').value;

            const url = `https://solana-gateway.moralis.io/token/mainnet/exchange/${exchange}/graduated?limit=${limit}`;

            try {
                const { data, status } = await makeRequest(url, {
                    headers: {
                        'X-API-Key': apiKey
                    }
                });
                displayResponse(data, status, `Graduated Tokens (${data?.result?.length || 0} tokens)`);
            } catch (error) {
                displayError(error.message, 'Moralis API Error');
            }
        }

        async function testMoralisBondingStatus() {
            const apiKey = getMoralisApiKey();
            if (!apiKey) return;

            displayLoading('Moralis API - Bonding Status');

            const tokenAddress = document.getElementById('moralis-status-token').value;
            if (!tokenAddress) {
                displayError('Please enter a token address', 'Token Address Required');
                return;
            }

            const url = `https://solana-gateway.moralis.io/token/mainnet/${tokenAddress}/bonding-status`;

            try {
                const { data, status } = await makeRequest(url, {
                    headers: {
                        'X-API-Key': apiKey
                    }
                });
                displayResponse(data, status, `Bonding Status: ${tokenAddress.substring(0, 8)}...`);
            } catch (error) {
                displayError(error.message, 'Moralis API Error');
            }
        }



        // Legacy API functions
        async function testLegacyFlags() {
            displayLoading('legacy-response');

            const url = 'https://pump.fun/api/flags';

            try {
                const { data, status } = await makeRequest(url);
                displayResponse('legacy-response', data, status);
            } catch (error) {
                displayError('legacy-response', error.message);
            }
        }

        async function testLegacyToken() {
            displayLoading('pumpfun-response');

            const mint = document.getElementById('legacy-mint').value;
            if (!mint) {
                displayError('pumpfun-response', 'Please enter a mint address');
                return;
            }

            const url = `https://pump.fun/api/coins/${mint}`;

            try {
                const { data, status } = await makeRequest(url);
                displayResponse('pumpfun-response', data, status);
            } catch (error) {
                displayError('pumpfun-response', error.message);
            }
        }

        async function testLegacyRunners() {
            displayLoading('Legacy API - Runners (Trending)');

            const url = 'https://pump.fun/api/runners';

            try {
                const { data, status } = await makeRequest(url);
                displayResponse({
                    success: true,
                    endpoint: '/api/runners',
                    description: 'Trending/running tokens with enhanced metadata',
                    count: data?.length || 0,
                    data: data,
                    note: 'Returns tokens with descriptions and modifiedBy fields'
                }, status, `Runners (${data?.length || 0} trending tokens)`);
            } catch (error) {
                displayError(error.message, 'Legacy API Error');
            }
        }

        async function testLegacyFlags() {
            displayLoading('pumpfun-response');

            const url = 'https://pump.fun/api/flags';

            try {
                const { data, status } = await makeRequest(url);
                displayResponse('pumpfun-response', {
                    success: true,
                    endpoint: '/api/flags',
                    description: 'Available flags and categories',
                    data: data,
                    note: 'Returns system flags and category information'
                }, status);
            } catch (error) {
                displayError('pumpfun-response', error.message);
            }
        }
    </script>
</body>
</html>
