const express = require('express');
const cors = require('cors');
const fetch = require('node-fetch');

const app = express();
const PORT = process.env.PORT || 3004;

// Enable CORS for all routes
app.use(cors());
app.use(express.json());

// In-memory cache (only forYou works from Frontend API v3)
let cache = {
    forYou: [],
    lastUpdated: null,
    updateInProgress: false
};

// Cache TTL (5 minutes)
const CACHE_TTL = 5 * 60 * 1000;

// Headers for pump.fun API requests
const getHeaders = () => ({
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    'Accept': 'application/json',
    'Origin': 'https://pump.fun',
    'Referer': 'https://pump.fun/',
    'Cache-Control': 'no-cache'
});

// Fetch data through CORS proxy with retry logic
async function fetchWithRetry(originalUrl, maxRetries = 3) {
    for (let attempt = 0; attempt < maxRetries; attempt++) {
        try {
            // Use CORS proxy on port 3003
            const urlObj = new URL(originalUrl);
            const proxyUrl = new URL('http://localhost:3003/api/proxy');

            // Add the base URL as the 'url' parameter
            proxyUrl.searchParams.set('url', urlObj.origin + urlObj.pathname);

            // Add all original query parameters
            urlObj.searchParams.forEach((value, key) => {
                proxyUrl.searchParams.set(key, value);
            });

            console.log(`Fetching: ${originalUrl} via proxy (attempt ${attempt + 1})`);
            const response = await fetch(proxyUrl.toString(), {
                timeout: 15000
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            // Get response text first to handle empty responses
            const responseText = await response.text();
            console.log(`📡 Response: ${originalUrl} - ${responseText.length} characters`);

            if (!responseText || responseText.trim() === '') {
                console.log(`⚠️ Empty response from: ${originalUrl}`);
                return [];
            }

            try {
                const data = JSON.parse(responseText);
                console.log(`✅ Success: ${originalUrl} - ${data.length || 0} tokens`);
                return data;
            } catch (jsonError) {
                console.log(`❌ JSON parse error for ${originalUrl}: ${jsonError.message}`);
                console.log(`Raw response: ${responseText.substring(0, 200)}...`);
                return [];
            }
        } catch (error) {
            console.log(`❌ Attempt ${attempt + 1} failed: ${error.message}`);
            if (attempt === maxRetries - 1) {
                throw error;
            }
            // Wait before retry
            await new Promise(resolve => setTimeout(resolve, 1000 * (attempt + 1)));
        }
    }
}

// Update cache with fresh data from pump.fun APIs
async function updateCache() {
    if (cache.updateInProgress) {
        console.log('Cache update already in progress, skipping...');
        return;
    }

    cache.updateInProgress = true;
    console.log('🔄 Starting cache update...');

    try {
        // Note: Frontend API v3 /coins endpoint doesn't work
        // Use Advanced API v2 for coins lists instead
        const trending = [];
        const newTokens = [];
        const volumeLeaders = [];

        // Fetch forYou tokens (pump.fun's recommendations)
        const forYouUrl = 'https://frontend-api-v3.pump.fun/coins/for-you?offset=0&limit=100&includeNsfw=false';
        const forYou = await fetchWithRetry(forYouUrl);

        // Note: Frontend API v3 graduated endpoint doesn't work, use Advanced API v2 instead

        // Update cache - only forYou works
        cache.forYou = Array.isArray(forYou) ? forYou : [];
        cache.lastUpdated = new Date().toISOString();

        console.log('✅ Cache updated successfully');
        console.log(`📊 For You: ${cache.forYou.length} tokens`);

    } catch (error) {
        console.error('❌ Cache update failed:', error.message);
    } finally {
        cache.updateInProgress = false;
    }
}

// Check if cache needs updating
function isCacheStale() {
    if (!cache.lastUpdated) return true;
    const lastUpdate = new Date(cache.lastUpdated);
    return Date.now() - lastUpdate.getTime() > CACHE_TTL;
}

// Middleware to ensure fresh cache
async function ensureFreshCache(req, res, next) {
    if (isCacheStale() && !cache.updateInProgress) {
        console.log('Cache is stale, updating...');
        updateCache(); // Don't await - let it update in background
    }
    next();
}

// API Routes

// Note: trending, new, and volume-leaders endpoints removed
// Frontend API v3 /coins endpoint doesn't work
// Use Advanced API v2 tab for coins lists instead

// Get forYou tokens (pump.fun recommendations)
app.get('/api/market/for-you', ensureFreshCache, (req, res) => {
    const limit = parseInt(req.query.limit) || 50;
    const offset = parseInt(req.query.offset) || 0;

    // Ensure cache.forYou is an array
    const forYouData = Array.isArray(cache.forYou) ? cache.forYou : [];
    const result = forYouData.slice(offset, offset + limit);

    res.json({
        success: true,
        category: 'for-you',
        description: 'Pump.fun algorithmic recommendations',
        total: forYouData.length,
        limit,
        offset,
        lastUpdated: cache.lastUpdated,
        data: result
    });
});

// Note: Graduated endpoint removed - Frontend API v3 doesn't work
// Use Advanced API v2 tab for graduated tokens instead

// Get all market data (only forYou works)
app.get('/api/market/all', ensureFreshCache, (req, res) => {
    const limit = parseInt(req.query.limit) || 20;

    const forYouData = Array.isArray(cache.forYou) ? cache.forYou : [];

    res.json({
        success: true,
        description: 'Available market data (for coins lists use Advanced API v2)',
        lastUpdated: cache.lastUpdated,
        note: 'Frontend API v3 /coins endpoint does not work. Use Advanced API v2 for trending, new, and volume data.',
        categories: {
            forYou: {
                description: 'Pump.fun algorithmic recommendations',
                count: forYouData.length,
                data: forYouData.slice(0, limit)
            }
        }
    });
});

// Cache status endpoint
app.get('/api/status', (req, res) => {
    res.json({
        status: 'ok',
        server: 'Market Cache Server',
        port: PORT,
        cache: {
            lastUpdated: cache.lastUpdated,
            isStale: isCacheStale(),
            updateInProgress: cache.updateInProgress,
            counts: {
                forYou: Array.isArray(cache.forYou) ? cache.forYou.length : 0
            },
            note: 'Only forYou endpoint works. Use Advanced API v2 for other token lists.'
        },
        timestamp: new Date().toISOString()
    });
});

// Force cache refresh
app.post('/api/refresh', async (req, res) => {
    try {
        await updateCache();
        res.json({
            success: true,
            message: 'Cache refreshed successfully',
            lastUpdated: cache.lastUpdated
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: 'Cache refresh failed',
            message: error.message
        });
    }
});

// Health check
app.get('/health', (req, res) => {
    res.json({
        status: 'ok',
        server: 'Market Cache Server',
        port: PORT,
        timestamp: new Date().toISOString()
    });
});

// Start server
app.listen(PORT, async () => {
    console.log(`🚀 Market Cache Server running on http://localhost:${PORT}`);
    console.log(`📊 Available endpoints:`);
    console.log(`   GET /api/market/for-you`);
    console.log(`   GET /api/market/all`);
    console.log(`💚 Health check: http://localhost:${PORT}/health`);
    console.log(`📈 Status: http://localhost:${PORT}/api/status`);
    console.log(`📝 Note: For coins lists, use Advanced API v2 tab (Frontend API v3 /coins doesn't work)`);
    
    // Initial cache load
    console.log('🔄 Loading initial cache...');
    await updateCache();
});

// Graceful shutdown
process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down Market Cache Server...');
    process.exit(0);
});

module.exports = app;
