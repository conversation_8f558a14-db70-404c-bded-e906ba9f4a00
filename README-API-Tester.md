# 🚀 Pump.fun API Tester

A beautiful, modern web interface for testing all pump.fun API endpoints. This tool provides an easy way to interact with and test the various APIs documented in your `API DOcs.md` file.

## Features

- **Modern Dark UI**: Beautiful dark theme with green accents matching the pump.fun aesthetic
- **Tabbed Interface**: Organized by API type for easy navigation
- **Real-time Testing**: Test endpoints with live data and see formatted responses
- **Copy Functionality**: Copy API responses with one click
- **Error Handling**: Clear error messages and status codes
- **Responsive Design**: Works on desktop and mobile devices

## API Endpoints Covered

### 1. PumpFun API
- **Frontend API v3**: Individual tokens, For You recommendations, Trading data
- **Advanced API v2**: Enhanced coins lists, Graduated tokens
- **Legacy API**: Token details, Runners (trending), Flags/Categories

### 2. Moralis API
- Get Token Bonding Status (requires API key)

### 4. Market Cache
- Get Trending Tokens
- Get New Tokens
- Get Volume Leaders
- Get For You Recommendations
- Get Graduated Tokens

### 5. Legacy API
- Get Flags/Categories
- Get Token Details

## How to Use

### 1. Open the Interface
Simply open `api-tester.html` in your web browser. No server setup required!

### 2. Navigate Between APIs
Use the tabs at the top to switch between different API types:
- **PumpFun API**: Complete pump.fun API suite (Frontend v3, Advanced v2, Legacy)
- **Moralis API**: Bonding curve data (requires API key)
- **Market Cache**: Market data aggregation server

### 3. Configure Parameters
Each endpoint has form fields for its parameters:
- **Sort options**: Choose how to sort results
- **Limits**: Set how many results to return (1-100)
- **Offsets**: For pagination
- **Mint addresses**: Test specific tokens
- **API keys**: For Moralis endpoints

### 4. Test Endpoints
Click the test buttons to make API calls. The interface will:
- Show a loading indicator
- Display the HTTP status code
- Format the JSON response with syntax highlighting
- Show timestamps for each request

### 5. Copy Responses
Use the "Copy" button to copy the full API response to your clipboard for further analysis or debugging.

## Example Usage

### Testing a Specific Token
1. Go to the "Frontend API v3" tab
2. Scroll to "Get Individual Token"
3. Enter a mint address (e.g., `9BB6NFEcjBCtnNLFko2FqVQBq8HHM13kCyYcdQbgpump`)
4. Click "Test Get Token"
5. View the formatted response with token details

### Getting Trending Tokens
1. Stay on the "Frontend API v3" tab
2. In "Get Coins List", set:
   - Sort Field: "Created Timestamp"
   - Order: "Descending"
   - Limit: "50"
   - Include NSFW: "No"
3. Click "Test Get Coins"
4. Browse the list of newest tokens

### Checking Market Cache Status
1. Go to the "Market Cache" tab
2. Click "Server Status"
3. View server health and cache metrics

## CORS Considerations

Some endpoints may be blocked by CORS policies when testing from a local file. If you encounter CORS errors:

1. **Use a local server**: Serve the HTML file through a local web server
2. **Browser extensions**: Use CORS-disabling browser extensions for testing
3. **Proxy server**: Set up a proxy server to bypass CORS restrictions

### Quick Local Server Setup
```bash
# Using Python
python -m http.server 8000

# Using Node.js
npx serve .

# Using PHP
php -S localhost:8000
```

Then access the tester at `http://localhost:8000/api-tester.html`

## API Key Setup

### Moralis API
1. Sign up at [moralis.io](https://moralis.io)
2. Get your API key from the dashboard
3. Enter it in the "Moralis API" tab when testing bonding status endpoints

## Response Format

All responses include:
- **Status Badge**: HTTP status code with color coding
- **Timestamp**: When the request was made
- **Formatted JSON**: Pretty-printed response data

### Status Code Colors
- 🟢 **Green (200-299)**: Successful requests
- 🔴 **Red (400+)**: Error responses

## Troubleshooting

### Common Issues

1. **CORS Errors**: Use a local server instead of opening the file directly
2. **Rate Limiting**: Wait between requests if you hit rate limits
3. **Invalid Mint Address**: Ensure mint addresses are valid Solana addresses
4. **Missing API Key**: Moralis endpoints require a valid API key

### Error Messages
The interface provides clear error messages for:
- Network failures
- Invalid parameters
- Missing required fields
- API rate limits
- Server errors

## Development

The tester is built with vanilla HTML, CSS, and JavaScript for maximum compatibility. Key features:

- **Responsive CSS Grid**: Adapts to different screen sizes
- **Modern Fetch API**: For making HTTP requests
- **ES6+ JavaScript**: Clean, modern code
- **CSS Custom Properties**: Easy theming and customization

## Contributing

To add new endpoints or improve the interface:

1. Add new form fields in the HTML
2. Create corresponding JavaScript test functions
3. Update the response handling as needed
4. Test thoroughly across different browsers

## License

This tool is provided as-is for testing and development purposes. Please respect API rate limits and terms of service for all endpoints.
